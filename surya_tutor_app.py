from flask import Flask, request, jsonify
from flask_cors import CORS
import openai
import requests
import random
import os
import json
import logging
from datetime import datetime
import hashlib
import time

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)
app.secret_key = os.environ.get('SECRET_KEY', 'learn-with-surya-secret-key')

# Initialize CORS
CORS(app, origins=['*'])

# OpenAI Configuration
openai.api_key = os.environ.get('OPENAI_API_KEY', '********************************************************************************************************************************************************************')

# Motivational quotes database
MOTIVATIONAL_QUOTES = [
    {
        "quote": "Success is not final, failure is not fatal: it is the courage to continue that counts.",
        "author": "<PERSON> <PERSON>",
        "category": "motivation"
    },
    {
        "quote": "The only way to do great work is to love what you do.",
        "author": "Steve Jobs",
        "category": "passion"
    },
    {
        "quote": "Innovation distinguishes between a leader and a follower.",
        "author": "Steve Jobs",
        "category": "innovation"
    },
    {
        "quote": "Your limitation—it's only your imagination.",
        "author": "Unknown",
        "category": "mindset"
    },
    {
        "quote": "Push yourself, because no one else is going to do it for you.",
        "author": "Unknown",
        "category": "self-motivation"
    },
    {
        "quote": "Great things never come from comfort zones.",
        "author": "Unknown",
        "category": "growth"
    },
    {
        "quote": "Dream it. Wish it. Do it.",
        "author": "Unknown",
        "category": "action"
    },
    {
        "quote": "Success doesn't just find you. You have to go out and get it.",
        "author": "Unknown",
        "category": "effort"
    },
    {
        "quote": "The harder you work for something, the greater you'll feel when you achieve it.",
        "author": "Unknown",
        "category": "achievement"
    },
    {
        "quote": "Don't stop when you're tired. Stop when you're done.",
        "author": "Unknown",
        "category": "persistence"
    },
    {
        "quote": "Wake up with determination. Go to bed with satisfaction.",
        "author": "Unknown",
        "category": "daily-motivation"
    },
    {
        "quote": "Do something today that your future self will thank you for.",
        "author": "Sean Patrick Flanery",
        "category": "future-thinking"
    },
    {
        "quote": "Little things make big days.",
        "author": "Unknown",
        "category": "progress"
    },
    {
        "quote": "It's going to be hard, but hard does not mean impossible.",
        "author": "Unknown",
        "category": "resilience"
    },
    {
        "quote": "Don't wait for opportunity. Create it.",
        "author": "Unknown",
        "category": "opportunity"
    }
]

# Subject to actor voice mapping
ACTOR_VOICES = {
    "cse": "suriya",
    "computer_science": "suriya",
    "mechanical": "sivakarthikeyan", 
    "electrical": "vijay",
    "civil": "ajith",
    "electronics": "dhanush",
    "chemical": "karthi",
    "mathematics": "suriya",
    "physics": "sivakarthikeyan",
    "chemistry": "vijay",
    "general": "suriya"
}

def get_gpt_response(question, subject_branch="general"):
    """Get AI response from OpenAI GPT-4"""
    try:
        # Create a friendly, educational prompt
        system_prompt = f"""You are Surya, a friendly and knowledgeable tutor specializing in {subject_branch}. 
        Your goal is to provide simple, clear, and encouraging explanations that help students understand concepts easily.
        
        Guidelines:
        1. Keep explanations simple and friendly
        2. Use examples when helpful
        3. Break down complex topics into easy steps
        4. Be encouraging and supportive
        5. Focus on practical understanding
        6. Keep responses concise but complete
        """
        
        response = openai.chat.completions.create(
            model="gpt-3.5-turbo",  # Using gpt-3.5-turbo instead of gpt-4
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": question}
            ],
            max_tokens=500,
            temperature=0.7
        )
        
        return response.choices[0].message.content
        
    except Exception as e:
        logger.error(f"Error getting GPT response: {str(e)}")
        return "I'm sorry, I'm having trouble processing your question right now. Please try again!"

def detect_subject_branch(question):
    """Detect subject branch from the question"""
    question_lower = question.lower()
    
    # Keywords for different subjects
    subject_keywords = {
        "cse": ["algorithm", "programming", "code", "software", "database", "computer", "data structure", "python", "java", "c++"],
        "mechanical": ["engine", "thermodynamics", "mechanics", "machine", "manufacturing", "heat", "fluid", "mechanical"],
        "electrical": ["circuit", "voltage", "current", "power", "electrical", "electronics", "motor", "generator"],
        "civil": ["construction", "concrete", "steel", "building", "structural", "civil", "bridge", "foundation"],
        "electronics": ["transistor", "diode", "amplifier", "digital", "analog", "signal", "communication"],
        "chemical": ["reaction", "chemical", "process", "catalyst", "polymer", "distillation", "separation"],
        "mathematics": ["equation", "calculus", "algebra", "geometry", "trigonometry", "derivative", "integral"],
        "physics": ["force", "energy", "motion", "wave", "quantum", "relativity", "physics", "momentum"],
        "chemistry": ["molecule", "atom", "bond", "organic", "inorganic", "chemistry", "compound"]
    }
    
    for subject, keywords in subject_keywords.items():
        if any(keyword in question_lower for keyword in keywords):
            return subject
    
    return "general"

def simulate_tts_generation(text, actor_voice, subject_branch):
    """Simulate TTS API call and return a mock audio URL"""
    try:
        # Create a unique filename based on content hash
        content_hash = hashlib.md5(f"{text}_{actor_voice}_{subject_branch}".encode()).hexdigest()[:8]
        timestamp = int(time.time())
        
        # Simulate TTS API call (placeholder)
        # In real implementation, you would call ElevenLabs or similar TTS API here
        
        # Mock TTS API response
        mock_audio_url = f"https://api.learnwithsurya.com/audio/{actor_voice}_{subject_branch}_{content_hash}_{timestamp}.mp3"
        
        logger.info(f"Generated TTS audio URL: {mock_audio_url} for actor: {actor_voice}")
        
        return {
            "audio_url": mock_audio_url,
            "actor_voice": actor_voice,
            "duration_seconds": len(text) // 10,  # Rough estimate
            "generated_at": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error generating TTS: {str(e)}")
        return {
            "audio_url": None,
            "error": "Failed to generate audio",
            "actor_voice": actor_voice
        }

@app.route('/', methods=['GET'])
def home():
    """Home endpoint"""
    return jsonify({
        "message": "Welcome to Learn with Surya - AI Tutoring Platform!",
        "version": "1.0.0",
        "endpoints": {
            "ask": "POST /ask - Ask a question and get AI response with voice",
            "quote": "GET /quote - Get a random motivational quote"
        },
        "features": [
            "AI-powered explanations",
            "Actor-style voice responses",
            "Subject-specific tutoring",
            "Motivational quotes"
        ]
    })

@app.route('/ask', methods=['POST'])
def ask_question():
    """Main endpoint to ask questions and get AI response with voice"""
    try:
        # Get request data
        data = request.get_json()
        if not data:
            return jsonify({"error": "No JSON data provided"}), 400
        
        question = data.get('question', '').strip()
        subject_branch = data.get('subject_branch', '').lower().strip()
        
        if not question:
            return jsonify({"error": "Question is required"}), 400
        
        # Auto-detect subject if not provided
        if not subject_branch:
            subject_branch = detect_subject_branch(question)
        
        # Get AI response
        ai_answer = get_gpt_response(question, subject_branch)
        
        # Get appropriate actor voice for the subject
        actor_voice = ACTOR_VOICES.get(subject_branch, "suriya")
        
        # Generate TTS audio (simulated)
        tts_result = simulate_tts_generation(ai_answer, actor_voice, subject_branch)
        
        # Prepare response
        response_data = {
            "question": question,
            "answer": ai_answer,
            "subject_branch": subject_branch,
            "actor_voice": actor_voice,
            "audio_url": tts_result.get("audio_url"),
            "audio_duration": tts_result.get("duration_seconds"),
            "timestamp": datetime.now().isoformat(),
            "success": True
        }
        
        # Add error info if TTS failed
        if tts_result.get("error"):
            response_data["audio_error"] = tts_result["error"]
        
        logger.info(f"Processed question: {question[:50]}... | Subject: {subject_branch} | Actor: {actor_voice}")
        
        return jsonify(response_data), 200
        
    except Exception as e:
        logger.error(f"Error in ask_question: {str(e)}")
        return jsonify({
            "error": "Internal server error",
            "message": "Sorry, something went wrong. Please try again.",
            "success": False
        }), 500

@app.route('/quote', methods=['GET'])
def get_motivational_quote():
    """Get a random motivational quote"""
    try:
        # Select a random quote
        quote_data = random.choice(MOTIVATIONAL_QUOTES)
        
        # Add timestamp and additional info
        response_data = {
            "quote": quote_data["quote"],
            "author": quote_data["author"],
            "category": quote_data["category"],
            "timestamp": datetime.now().isoformat(),
            "success": True
        }
        
        logger.info(f"Served motivational quote: {quote_data['category']}")
        
        return jsonify(response_data), 200
        
    except Exception as e:
        logger.error(f"Error in get_motivational_quote: {str(e)}")
        return jsonify({
            "error": "Failed to get quote",
            "success": False
        }), 500

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0",
        "services": {
            "openai": "connected" if openai.api_key else "not_configured",
            "tts": "simulated"
        }
    }), 200

if __name__ == '__main__':
    print("🎓 Starting Learn with Surya - AI Tutoring Platform")
    print("=" * 50)
    print("Available endpoints:")
    print("  POST /ask    - Ask questions and get AI responses with voice")
    print("  GET  /quote  - Get motivational quotes")
    print("  GET  /health - Health check")
    print("=" * 50)
    
    app.run(debug=True, host='0.0.0.0', port=5000)
