import re
import json
import logging
from datetime import datetime
from functools import wraps
from flask import session, jsonify, request
import sqlite3

logger = logging.getLogger(__name__)

def require_auth(f):
    """Decorator to require authentication for endpoints"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return jsonify({"error": "Authentication required"}), 401
        return f(*args, **kwargs)
    return decorated_function

def validate_email(email):
    """Validate email format"""
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def validate_password(password):
    """Validate password strength"""
    if len(password) < 8:
        return False, "Password must be at least 8 characters long"
    if not re.search(r'[A-Za-z]', password):
        return False, "Password must contain at least one letter"
    if not re.search(r'\d', password):
        return False, "Password must contain at least one number"
    return True, "Password is valid"

def sanitize_input(text):
    """Sanitize user input to prevent injection attacks"""
    if not text:
        return ""
    # Remove potentially dangerous characters
    sanitized = re.sub(r'[<>"\']', '', str(text))
    return sanitized.strip()

def format_response(data, status_code=200):
    """Format API response consistently"""
    response = {
        "timestamp": datetime.now().isoformat(),
        "status": "success" if status_code < 400 else "error"
    }
    response.update(data)
    return jsonify(response), status_code

def log_user_activity(user_id, activity, details=None):
    """Log user activity for analytics"""
    try:
        log_entry = {
            "user_id": user_id,
            "activity": activity,
            "details": details,
            "timestamp": datetime.now().isoformat(),
            "ip_address": request.remote_addr,
            "user_agent": request.headers.get('User-Agent', '')
        }
        logger.info(f"User Activity: {json.dumps(log_entry)}")
    except Exception as e:
        logger.error(f"Failed to log user activity: {str(e)}")

def get_user_by_id(user_id, db_path):
    """Get user information by ID"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute('''
            SELECT id, username, email, created_at 
            FROM users WHERE id = ?
        ''', (user_id,))
        user = cursor.fetchone()
        conn.close()
        
        if user:
            return {
                "id": user[0],
                "username": user[1],
                "email": user[2],
                "created_at": user[3]
            }
        return None
    except Exception as e:
        logger.error(f"Error fetching user: {str(e)}")
        return None

def calculate_learning_streak(user_id, db_path):
    """Calculate user's learning streak"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute('''
            SELECT DATE(created_at) as study_date
            FROM qa_history 
            WHERE user_id = ?
            GROUP BY DATE(created_at)
            ORDER BY study_date DESC
        ''', (user_id,))
        
        study_dates = [row[0] for row in cursor.fetchall()]
        conn.close()
        
        if not study_dates:
            return 0
        
        # Calculate consecutive days
        streak = 1
        current_date = datetime.strptime(study_dates[0], '%Y-%m-%d').date()
        
        for i in range(1, len(study_dates)):
            prev_date = datetime.strptime(study_dates[i], '%Y-%m-%d').date()
            if (current_date - prev_date).days == 1:
                streak += 1
                current_date = prev_date
            else:
                break
        
        return streak
    except Exception as e:
        logger.error(f"Error calculating streak: {str(e)}")
        return 0

def get_popular_topics(db_path, limit=10):
    """Get most popular topics based on questions asked"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute('''
            SELECT category, COUNT(*) as question_count
            FROM qa_history 
            GROUP BY category
            ORDER BY question_count DESC
            LIMIT ?
        ''', (limit,))
        
        topics = []
        for row in cursor.fetchall():
            topics.append({
                "category": row[0],
                "question_count": row[1]
            })
        
        conn.close()
        return topics
    except Exception as e:
        logger.error(f"Error fetching popular topics: {str(e)}")
        return []

def generate_study_recommendations(user_id, db_path):
    """Generate personalized study recommendations"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get user's weak areas (categories with fewer questions)
        cursor.execute('''
            SELECT category, COUNT(*) as count
            FROM qa_history 
            WHERE user_id = ?
            GROUP BY category
            ORDER BY count ASC
            LIMIT 3
        ''', (user_id,))
        
        weak_areas = [row[0] for row in cursor.fetchall()]
        
        # Get user's recent activity
        cursor.execute('''
            SELECT category, difficulty_level
            FROM qa_history 
            WHERE user_id = ?
            ORDER BY created_at DESC
            LIMIT 10
        ''', (user_id,))
        
        recent_activity = cursor.fetchall()
        conn.close()
        
        recommendations = []
        
        # Recommend weak areas
        for area in weak_areas:
            recommendations.append({
                "type": "weak_area",
                "category": area,
                "message": f"Practice more questions in {area} to strengthen your understanding"
            })
        
        # Recommend difficulty progression
        if recent_activity:
            recent_difficulties = [row[1] for row in recent_activity]
            if recent_difficulties.count('Easy') > len(recent_difficulties) * 0.7:
                recommendations.append({
                    "type": "difficulty_progression",
                    "message": "Try some Medium difficulty questions to challenge yourself"
                })
        
        return recommendations[:5]  # Return top 5 recommendations
        
    except Exception as e:
        logger.error(f"Error generating recommendations: {str(e)}")
        return []

class RateLimiter:
    """Simple in-memory rate limiter"""
    def __init__(self):
        self.requests = {}
    
    def is_allowed(self, identifier, limit_per_minute=30):
        """Check if request is allowed based on rate limit"""
        now = datetime.now()
        minute_key = now.strftime('%Y-%m-%d %H:%M')
        
        if identifier not in self.requests:
            self.requests[identifier] = {}
        
        if minute_key not in self.requests[identifier]:
            self.requests[identifier][minute_key] = 0
        
        # Clean old entries
        for key in list(self.requests[identifier].keys()):
            if key < minute_key:
                del self.requests[identifier][key]
        
        if self.requests[identifier][minute_key] >= limit_per_minute:
            return False
        
        self.requests[identifier][minute_key] += 1
        return True

# Global rate limiter instance
rate_limiter = RateLimiter()

def apply_rate_limit(limit_per_minute=30):
    """Decorator to apply rate limiting"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            identifier = request.remote_addr
            if not rate_limiter.is_allowed(identifier, limit_per_minute):
                return jsonify({"error": "Rate limit exceeded"}), 429
            return f(*args, **kwargs)
        return decorated_function
    return decorator
