#!/usr/bin/env python3
"""
Setup script for Learn with Surya AI Tutoring Platform
"""

import subprocess
import sys
import os

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"🔧 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        print(f"Error output: {e.stderr}")
        return False

def check_python_version():
    """Check if Python version is compatible"""
    print("🐍 Checking Python version...")
    version = sys.version_info
    if version.major == 3 and version.minor >= 8:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} is compatible")
        return True
    else:
        print(f"❌ Python {version.major}.{version.minor}.{version.micro} is not compatible. Please use Python 3.8+")
        return False

def create_virtual_environment():
    """Create virtual environment if it doesn't exist"""
    if not os.path.exists('.venv'):
        print("📦 Creating virtual environment...")
        return run_command("python -m venv .venv", "Virtual environment creation")
    else:
        print("✅ Virtual environment already exists")
        return True

def activate_virtual_environment():
    """Instructions for activating virtual environment"""
    print("\n💡 To activate the virtual environment:")
    if os.name == 'nt':  # Windows
        print("   .venv\\Scripts\\activate")
    else:  # Unix/Linux/Mac
        print("   source .venv/bin/activate")

def install_dependencies():
    """Install required dependencies"""
    dependencies = [
        "Flask==3.1.1",
        "Flask-CORS==6.0.1", 
        "openai==1.97.0",
        "requests==2.32.4",
        "python-dotenv==1.1.1",
        "pytest==8.4.1",
        "pytest-flask==1.3.0"
    ]
    
    print("📚 Installing dependencies...")
    for dep in dependencies:
        if not run_command(f"pip install {dep}", f"Installing {dep}"):
            return False
    return True

def create_env_file():
    """Create .env file template"""
    env_content = """# Learn with Surya - Environment Variables
# Copy this file to .env and update with your actual values

# OpenAI API Configuration
OPENAI_API_KEY=your-openai-api-key-here

# Flask Configuration
FLASK_ENV=development
SECRET_KEY=your-secret-key-here

# Server Configuration
FLASK_HOST=0.0.0.0
FLASK_PORT=5000
FLASK_DEBUG=True

# TTS Configuration (for future implementation)
ELEVENLABS_API_KEY=your-elevenlabs-api-key-here
"""
    
    if not os.path.exists('.env'):
        print("📝 Creating .env template file...")
        with open('.env.template', 'w') as f:
            f.write(env_content)
        print("✅ Created .env.template file")
        print("💡 Copy .env.template to .env and update with your API keys")
    else:
        print("✅ .env file already exists")

def verify_installation():
    """Verify that all dependencies are installed correctly"""
    print("🔍 Verifying installation...")
    
    try:
        import flask
        import flask_cors
        import openai
        import requests
        import pytest
        print("✅ All core dependencies are installed correctly")
        return True
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

def main():
    """Main setup function"""
    print("🎓 Learn with Surya AI Tutoring Platform Setup")
    print("=" * 60)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Create virtual environment
    if not create_virtual_environment():
        sys.exit(1)
    
    # Show activation instructions
    activate_virtual_environment()
    
    # Install dependencies
    if not install_dependencies():
        sys.exit(1)
    
    # Create environment file template
    create_env_file()
    
    # Verify installation
    if not verify_installation():
        sys.exit(1)
    
    print("\n" + "=" * 60)
    print("🎉 Setup completed successfully!")
    print("\n📋 Next Steps:")
    print("1. Activate the virtual environment (see instructions above)")
    print("2. Copy .env.template to .env and add your OpenAI API key")
    print("3. Run the application: python surya_tutor_app.py")
    print("4. Test the API: python test_backend.py")
    print("\n🌐 The application will be available at: http://localhost:5000")
    print("\n📚 Available endpoints:")
    print("   POST /ask    - Ask questions and get AI responses with voice")
    print("   GET  /quote  - Get motivational quotes")
    print("   GET  /health - Health check")

if __name__ == "__main__":
    main()
