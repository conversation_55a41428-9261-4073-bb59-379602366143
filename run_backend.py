#!/usr/bin/env python3
"""
Simple script to run the Learn with <PERSON>ya backend
"""

import os
import sys
from app import app

def main():
    """Main function to run the Flask application"""
    print("🎓 Starting Learn with Surya Backend...")
    print("=" * 50)
    
    # Check if OpenAI API key is configured
    if not app.config.get('OPENAI_API_KEY') or app.config['OPENAI_API_KEY'].startswith('your-'):
        print("⚠️  Warning: OpenAI API key not properly configured!")
        print("   Please set your API key in config.py or environment variable OPENAI_API_KEY")
        print()
    
    # Display configuration
    print(f"🌐 Server will run on: http://localhost:5000")
    print(f"🔧 Environment: {os.environ.get('FLASK_ENV', 'development')}")
    print(f"🤖 AI Model: {getattr(app.config, 'AI_MODEL', 'gpt-4')}")
    print()
    
    # Available endpoints
    print("📋 Available API Endpoints:")
    print("   GET  /                    - Home page")
    print("   POST /ask                 - Ask a question")
    print("   POST /api/register        - User registration")
    print("   POST /api/login           - User login")
    print("   POST /api/logout          - User logout")
    print("   GET  /api/history         - Get question history")
    print("   GET  /api/stats           - Get user statistics")
    print("   POST /api/generate-quiz   - Generate custom quiz")
    print("   POST /api/explain-concept - Explain concepts")
    print("   GET  /api/health          - Health check")
    print()
    
    print("🚀 Starting server...")
    print("   Press Ctrl+C to stop the server")
    print("=" * 50)
    
    try:
        # Run the Flask application
        app.run(
            debug=True,
            host='0.0.0.0',
            port=5000,
            use_reloader=True
        )
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
    except Exception as e:
        print(f"\n❌ Error starting server: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
