# 🎓 Learn with Surya - AI Tutoring Platform

An intelligent tutoring platform powered by OpenAI GPT that provides personalized educational responses with actor-style voice synthesis for different engineering subjects.

## ✨ Features

### 🤖 AI-Powered Education
- **Smart Question Answering**: Uses OpenAI GPT-3.5-turbo for educational responses
- **Subject Detection**: Automatically categorizes questions by engineering branch
- **Friendly Explanations**: Provides simple, clear, and encouraging explanations
- **Step-by-step Solutions**: Breaks down complex topics into easy steps

### 🎭 Actor-Style Voice Responses
- **Subject-Based Voice Mapping**: Different Tamil actors for different subjects
  - CSE/Computer Science → Suriya
  - Mechanical → Sivakarthikeyan
  - Electrical → Vijay
  - Civil → Ajith
  - Electronics → Dhanush
  - Chemical → Karthi
- **TTS Integration**: Simulated Text-to-Speech with mock audio URLs
- **Voice Customization**: Easy to extend with real TTS APIs

### 💬 Motivational Support
- **Random Quotes**: Inspirational quotes to keep students motivated
- **Categorized Messages**: Different types of motivational content
- **Daily Inspiration**: Perfect for study session motivation

## 🚀 Quick Start

### Prerequisites
- Python 3.8 or higher
- OpenAI API key
- Virtual environment (recommended)

### Installation

1. **Clone or download the project**
```bash
cd "learn_with_surya project use case"
```

2. **Run the setup script**
```bash
python setup.py
```

3. **Activate virtual environment**
```bash
# Windows
.venv\Scripts\activate

# Linux/Mac
source .venv/bin/activate
```

4. **Configure environment variables**
```bash
# Copy the template
copy .env.template .env

# Edit .env and add your OpenAI API key
OPENAI_API_KEY=your-actual-api-key-here
```

5. **Start the application**
```bash
python surya_tutor_app.py
```

6. **Test the API**
```bash
python test_backend.py
```

## 📡 API Endpoints

### POST /ask
Ask educational questions and get AI responses with voice.

**Request:**
```json
{
    "question": "What is Ohm's law?",
    "subject_branch": "electrical"  // optional
}
```

**Response:**
```json
{
    "question": "What is Ohm's law?",
    "answer": "Ohm's law states that...",
    "subject_branch": "electrical",
    "actor_voice": "vijay",
    "audio_url": "https://api.learnwithsurya.com/audio/vijay_electrical_abc123.mp3",
    "audio_duration": 45,
    "timestamp": "2024-01-15T10:30:00",
    "success": true
}
```

### GET /quote
Get random motivational quotes.

**Response:**
```json
{
    "quote": "Success is not final, failure is not fatal...",
    "author": "Winston Churchill",
    "category": "motivation",
    "timestamp": "2024-01-15T10:30:00",
    "success": true
}
```

### GET /health
Health check endpoint.

**Response:**
```json
{
    "status": "healthy",
    "timestamp": "2024-01-15T10:30:00",
    "version": "1.0.0",
    "services": {
        "openai": "connected",
        "tts": "simulated"
    }
}
```

## 🏗️ Project Structure

```
learn_with_surya/
├── surya_tutor_app.py      # Main Flask application
├── test_backend.py         # API testing script
├── setup.py               # Setup and installation script
├── requirements.txt       # Python dependencies
├── config.py             # Configuration management
├── utils.py              # Utility functions
├── API_DOCUMENTATION.md  # Detailed API docs
├── README.md             # This file
├── .env.template         # Environment variables template
├── templates/            # HTML templates
│   └── index.html
└── static/              # Static files
    └── style.css
```

## 🔧 Configuration

### Environment Variables
Create a `.env` file with the following variables:

```env
# OpenAI Configuration
OPENAI_API_KEY=your-openai-api-key

# Flask Configuration
FLASK_ENV=development
SECRET_KEY=your-secret-key
FLASK_HOST=0.0.0.0
FLASK_PORT=5000
FLASK_DEBUG=True

# TTS Configuration (future)
ELEVENLABS_API_KEY=your-elevenlabs-key
```

### Subject-Actor Mapping
Customize the voice mapping in `surya_tutor_app.py`:

```python
ACTOR_VOICES = {
    "cse": "suriya",
    "mechanical": "sivakarthikeyan",
    "electrical": "vijay",
    "civil": "ajith",
    "electronics": "dhanush",
    "chemical": "karthi",
    # Add more mappings
}
```

## 🧪 Testing

Run the comprehensive test suite:

```bash
python test_backend.py
```

The test script will verify:
- ✅ Health check endpoint
- ✅ Question answering with subject specification
- ✅ Auto-detection of subject from question
- ✅ Motivational quote generation

## 🔮 Future Enhancements

### Real TTS Integration
Replace the simulated TTS with actual services:

```python
def integrate_elevenlabs_tts(text, voice_id):
    """Integrate with ElevenLabs TTS API"""
    # Implementation here
    pass
```

### Additional Features
- User authentication and profiles
- Learning progress tracking
- Question history and analytics
- Mobile app integration
- Real-time voice chat
- Multilingual support

## 🛠️ Development

### Adding New Subjects
1. Update `ACTOR_VOICES` mapping
2. Add keywords to `detect_subject_branch()`
3. Test with sample questions

### Customizing AI Responses
Modify the system prompt in `get_gpt_response()`:

```python
system_prompt = f"""You are Surya, a friendly tutor...
Add your custom instructions here...
"""
```

## 📦 Dependencies

### Core Dependencies
- **Flask 3.1.1** - Web framework
- **Flask-CORS 6.0.1** - Cross-origin resource sharing
- **OpenAI 1.97.0** - AI integration
- **Requests 2.32.4** - HTTP client

### Development Dependencies
- **pytest 8.4.1** - Testing framework
- **pytest-flask 1.3.0** - Flask testing utilities

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new features
5. Run the test suite
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For issues and questions:
1. Check the API documentation
2. Run the test suite to verify setup
3. Check the logs for error messages
4. Ensure your OpenAI API key is valid

## 🎯 Use Cases

- **GATE Exam Preparation**
- **Engineering Student Tutoring**
- **Concept Clarification**
- **Study Session Motivation**
- **Quick Reference and Help**

---

**Built with ❤️ for students by students**
