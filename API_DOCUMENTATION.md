# Learn with <PERSON>ya - Backend API Documentation

## Overview
This is the backend API for the "Learn with Surya" educational platform, designed specifically for GATE exam preparation with AI-powered tutoring.

## Base URL
```
http://localhost:5000
```

## Authentication
The API uses session-based authentication. Users need to register/login to access personalized features.

## Endpoints

### 1. Home Page
**GET** `/`
- Returns the main HTML page
- No authentication required

### 2. Ask Question
**POST** `/ask`
- Submit a question and get AI-powered educational response
- **Content-Type**: `application/x-www-form-urlencoded` or `application/json`

**Request Body (Form):**
```
question: "Explain <PERSON><PERSON>'s laws"
```

**Request Body (JSON):**
```json
{
    "question": "Explain <PERSON>'s laws"
}
```

**Response:**
```json
{
    "question": "Explain <PERSON>'s laws",
    "answer": "Detailed AI-generated explanation...",
    "category": "Electronics",
    "difficulty": "Medium",
    "timestamp": "2024-01-15T10:30:00"
}
```

### 3. User Registration
**POST** `/api/register`
- Register a new user account

**Request Body:**
```json
{
    "username": "student123",
    "email": "<EMAIL>",
    "password": "securepassword"
}
```

**Response:**
```json
{
    "message": "Registration successful",
    "user_id": 1,
    "username": "student123"
}
```

### 4. User Login
**POST** `/api/login`
- Login with existing credentials

**Request Body:**
```json
{
    "username": "student123",
    "password": "securepassword"
}
```

**Response:**
```json
{
    "message": "Login successful",
    "user_id": 1,
    "username": "student123"
}
```

### 5. User Logout
**POST** `/api/logout`
- Logout current user
- Requires authentication

**Response:**
```json
{
    "message": "Logout successful"
}
```

### 6. Get Question History
**GET** `/api/history`
- Get user's question and answer history
- Requires authentication

**Response:**
```json
{
    "history": [
        {
            "question": "Explain Kirchhoff's laws",
            "answer": "Detailed explanation...",
            "category": "Electronics",
            "difficulty": "Medium",
            "timestamp": "2024-01-15T10:30:00"
        }
    ]
}
```

### 7. Get User Statistics
**GET** `/api/stats`
- Get user's learning statistics
- Requires authentication

**Response:**
```json
{
    "total_questions": 25,
    "category_breakdown": {
        "Mathematics": 10,
        "Electronics": 8,
        "Computer Science": 7
    },
    "difficulty_breakdown": {
        "Easy": 5,
        "Medium": 15,
        "Hard": 5
    }
}
```

### 8. Generate Quiz
**POST** `/api/generate-quiz`
- Generate a custom quiz on specific topics

**Request Body:**
```json
{
    "topic": "Digital Electronics",
    "num_questions": 5,
    "difficulty": "Medium"
}
```

**Response:**
```json
{
    "topic": "Digital Electronics",
    "difficulty": "Medium",
    "num_questions": 5,
    "quiz_content": "Generated quiz questions...",
    "generated_at": "2024-01-15T10:30:00"
}
```

### 9. Explain Concept
**POST** `/api/explain-concept`
- Get detailed explanation of a specific concept

**Request Body:**
```json
{
    "concept": "Fourier Transform",
    "level": "intermediate"
}
```

**Response:**
```json
{
    "concept": "Fourier Transform",
    "level": "intermediate",
    "explanation": "Detailed concept explanation...",
    "generated_at": "2024-01-15T10:30:00"
}
```

### 10. Health Check
**GET** `/api/health`
- Check API health status

**Response:**
```json
{
    "status": "healthy",
    "timestamp": "2024-01-15T10:30:00",
    "version": "1.0.0"
}
```

## Error Responses

All endpoints return appropriate HTTP status codes and error messages:

**400 Bad Request:**
```json
{
    "error": "Question cannot be empty"
}
```

**401 Unauthorized:**
```json
{
    "error": "Authentication required"
}
```

**409 Conflict:**
```json
{
    "error": "Username or email already exists"
}
```

**500 Internal Server Error:**
```json
{
    "error": "Internal server error"
}
```

## Features

### AI Integration
- **OpenAI GPT-4** for educational responses
- Specialized prompts for GATE preparation
- Question categorization and difficulty assessment
- Concept explanations and quiz generation

### Database Features
- User management with secure password hashing
- Question/answer history tracking
- Learning statistics and progress tracking
- Session management

### Educational Features
- GATE-focused content delivery
- Multiple difficulty levels
- Subject categorization
- Personalized learning history
- Custom quiz generation
- Detailed concept explanations

## Security Features
- Password hashing with Werkzeug
- Session-based authentication
- CORS protection
- Input validation and sanitization
- Error handling and logging

## Getting Started

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Set environment variables (optional):
```bash
export OPENAI_API_KEY="your-openai-api-key"
export SECRET_KEY="your-secret-key"
```

3. Run the application:
```bash
python app.py
```

The API will be available at `http://localhost:5000`
