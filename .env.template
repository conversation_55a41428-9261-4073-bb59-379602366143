# Learn with Surya - Environment Variables Template
# Copy this file to .env and update with your actual values

# OpenAI API Configuration
OPENAI_API_KEY=your-openai-api-key-here

# Flask Configuration
FLASK_ENV=development
SECRET_KEY=your-secret-key-here

# Server Configuration
FLASK_HOST=0.0.0.0
FLASK_PORT=5000
FLASK_DEBUG=True

# TTS Configuration (for future implementation)
ELEVENLABS_API_KEY=your-elevenlabs-api-key-here

# Database Configuration (if needed)
DATABASE_URL=sqlite:///learn_with_surya.db

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=app.log

# Rate Limiting
RATE_LIMIT_PER_MINUTE=30

# CORS Configuration
CORS_ORIGINS=*
