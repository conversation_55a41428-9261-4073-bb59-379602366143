from flask import Flask, render_template, request, jsonify, session
from flask_cors import CORS
import openai
import os
import json
import logging
from datetime import datetime
import sqlite3
from werkzeug.security import generate_password_hash, check_password_hash
import uuid

# Import custom modules
from config import config
from utils import (
    require_auth, validate_email, validate_password, sanitize_input,
    format_response, log_user_activity, apply_rate_limit, rate_limiter
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)

# Load configuration
env = os.environ.get('FLASK_ENV', 'development')
app.config.from_object(config[env])

# Initialize CORS
CORS(app, origins=app.config['CORS_ORIGINS'])

# OpenAI Configuration
openai.api_key = app.config['OPENAI_API_KEY']

class DatabaseManager:
    def __init__(self, db_path="learn_with_surya.db"):
        self.db_path = db_path
        self.init_database()

    def init_database(self):
        """Initialize the database with required tables"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # Users table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                email TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # Questions and answers table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS qa_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                question TEXT NOT NULL,
                answer TEXT NOT NULL,
                category TEXT,
                difficulty_level TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')

        # Study sessions table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS study_sessions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                session_id TEXT UNIQUE NOT NULL,
                topic TEXT,
                questions_asked INTEGER DEFAULT 0,
                correct_answers INTEGER DEFAULT 0,
                start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                end_time TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')

        conn.commit()
        conn.close()
        logger.info("Database initialized successfully")

class AIService:
    def __init__(self):
        self.client = openai

    def get_educational_response(self, question, context=None):
        """Get AI response optimized for educational content"""
        try:
            # Create a specialized prompt for GATE preparation
            system_prompt = """You are Surya, an expert GATE (Graduate Aptitude Test in Engineering) tutor.
            Your role is to help students learn and understand engineering concepts effectively.

            Guidelines:
            1. Provide clear, detailed explanations
            2. Include step-by-step solutions for problems
            3. Add relevant formulas and concepts
            4. Suggest related topics for further study
            5. Use simple language but maintain technical accuracy
            6. Include practical examples when possible
            7. For numerical problems, show complete working

            Always end your response with a related question to test understanding."""

            user_prompt = f"Question: {question}"
            if context:
                user_prompt += f"\nContext: {context}"

            response = self.client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                max_tokens=1000,
                temperature=0.7
            )

            return response.choices[0].message.content

        except Exception as e:
            logger.error(f"Error getting AI response: {str(e)}")
            return "I'm sorry, I'm having trouble processing your question right now. Please try again."

    def categorize_question(self, question):
        """Categorize the question by subject area"""
        try:
            response = self.client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "Categorize this GATE question into one of these subjects: Mathematics, Physics, Chemistry, Computer Science, Electronics, Mechanical, Civil, Electrical, Chemical, or General. Respond with just the subject name."},
                    {"role": "user", "content": question}
                ],
                max_tokens=20,
                temperature=0.3
            )
            content = response.choices[0].message.content
            return content.strip() if content else "Medium"
        except:
            return "General"

    def assess_difficulty(self, question):
        """Assess the difficulty level of the question"""
        try:
            response = self.client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "Rate this GATE question difficulty as: Easy, Medium, or Hard. Respond with just the difficulty level."},
                    {"role": "user", "content": question}
                ],
                max_tokens=10,
                temperature=0.3
            )
            content = response.choices[0].message.content
            return content.strip() if content else "General"
        except:
            return "Medium"

# Initialize services
db_manager = DatabaseManager()
ai_service = AIService()

@app.route("/")
def home():
    return render_template("index.html")

@app.route("/ask", methods=["POST"])
@apply_rate_limit(limit_per_minute=20)
def ask():
    try:
        question = request.form.get("question", "").strip()
        if not question:
            return jsonify({"error": "Question cannot be empty"}), 400

        # Get AI response
        answer = ai_service.get_educational_response(question)
        category = ai_service.categorize_question(question)
        difficulty = ai_service.assess_difficulty(question)

        # Store in database (if user is logged in)
        user_id = session.get('user_id')
        if user_id:
            conn = sqlite3.connect(db_manager.db_path)
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO qa_history (user_id, question, answer, category, difficulty_level)
                VALUES (?, ?, ?, ?, ?)
            ''', (user_id, question, answer, category, difficulty))
            conn.commit()
            conn.close()

        # Return response
        if request.content_type == 'application/json':
            return jsonify({
                "question": question,
                "answer": answer,
                "category": category,
                "difficulty": difficulty,
                "timestamp": datetime.now().isoformat()
            })
        else:
            return render_template("index.html",
                                 question=question,
                                 answer=answer,
                                 category=category,
                                 difficulty=difficulty)

    except Exception as e:
        logger.error(f"Error in ask endpoint: {str(e)}")
        if request.content_type == 'application/json':
            return jsonify({"error": "Internal server error"}), 500
        else:
            return render_template("index.html", error="Something went wrong. Please try again.")

@app.route("/api/register", methods=["POST"])
def register():
    """User registration endpoint"""
    try:
        data = request.get_json()
        username = data.get('username')
        email = data.get('email')
        password = data.get('password')

        if not all([username, email, password]):
            return jsonify({"error": "All fields are required"}), 400

        # Hash password
        password_hash = generate_password_hash(password)

        # Store user
        conn = sqlite3.connect(db_manager.db_path)
        cursor = conn.cursor()
        try:
            cursor.execute('''
                INSERT INTO users (username, email, password_hash)
                VALUES (?, ?, ?)
            ''', (username, email, password_hash))
            conn.commit()
            user_id = cursor.lastrowid

            session['user_id'] = user_id
            session['username'] = username

            return jsonify({
                "message": "Registration successful",
                "user_id": user_id,
                "username": username
            }), 201

        except sqlite3.IntegrityError:
            return jsonify({"error": "Username or email already exists"}), 409
        finally:
            conn.close()

    except Exception as e:
        logger.error(f"Registration error: {str(e)}")
        return jsonify({"error": "Registration failed"}), 500

@app.route("/api/login", methods=["POST"])
def login():
    """User login endpoint"""
    try:
        data = request.get_json()
        username = data.get('username')
        password = data.get('password')

        if not all([username, password]):
            return jsonify({"error": "Username and password are required"}), 400

        conn = sqlite3.connect(db_manager.db_path)
        cursor = conn.cursor()
        cursor.execute('''
            SELECT id, username, password_hash FROM users
            WHERE username = ? OR email = ?
        ''', (username, username))

        user = cursor.fetchone()
        conn.close()

        if user and check_password_hash(user[2], password):
            session['user_id'] = user[0]
            session['username'] = user[1]

            return jsonify({
                "message": "Login successful",
                "user_id": user[0],
                "username": user[1]
            }), 200
        else:
            return jsonify({"error": "Invalid credentials"}), 401

    except Exception as e:
        logger.error(f"Login error: {str(e)}")
        return jsonify({"error": "Login failed"}), 500

@app.route("/api/logout", methods=["POST"])
def logout():
    """User logout endpoint"""
    session.clear()
    return jsonify({"message": "Logout successful"}), 200

@app.route("/api/history", methods=["GET"])
def get_history():
    """Get user's question history"""
    user_id = session.get('user_id')
    if not user_id:
        return jsonify({"error": "Authentication required"}), 401

    try:
        conn = sqlite3.connect(db_manager.db_path)
        cursor = conn.cursor()
        cursor.execute('''
            SELECT question, answer, category, difficulty_level, created_at
            FROM qa_history
            WHERE user_id = ?
            ORDER BY created_at DESC
            LIMIT 50
        ''', (user_id,))

        history = []
        for row in cursor.fetchall():
            history.append({
                "question": row[0],
                "answer": row[1],
                "category": row[2],
                "difficulty": row[3],
                "timestamp": row[4]
            })

        conn.close()
        return jsonify({"history": history}), 200

    except Exception as e:
        logger.error(f"History error: {str(e)}")
        return jsonify({"error": "Failed to fetch history"}), 500

@app.route("/api/stats", methods=["GET"])
def get_stats():
    """Get user statistics"""
    user_id = session.get('user_id')
    if not user_id:
        return jsonify({"error": "Authentication required"}), 401

    try:
        conn = sqlite3.connect(db_manager.db_path)
        cursor = conn.cursor()

        # Total questions asked
        cursor.execute('SELECT COUNT(*) FROM qa_history WHERE user_id = ?', (user_id,))
        total_questions = cursor.fetchone()[0]

        # Questions by category
        cursor.execute('''
            SELECT category, COUNT(*)
            FROM qa_history
            WHERE user_id = ?
            GROUP BY category
        ''', (user_id,))
        category_stats = dict(cursor.fetchall())

        # Questions by difficulty
        cursor.execute('''
            SELECT difficulty_level, COUNT(*)
            FROM qa_history
            WHERE user_id = ?
            GROUP BY difficulty_level
        ''', (user_id,))
        difficulty_stats = dict(cursor.fetchall())

        conn.close()

        return jsonify({
            "total_questions": total_questions,
            "category_breakdown": category_stats,
            "difficulty_breakdown": difficulty_stats
        }), 200

    except Exception as e:
        logger.error(f"Stats error: {str(e)}")
        return jsonify({"error": "Failed to fetch statistics"}), 500

@app.route("/api/generate-quiz", methods=["POST"])
def generate_quiz():
    """Generate a quiz based on topic"""
    try:
        data = request.get_json()
        topic = data.get('topic', 'General GATE topics')
        num_questions = min(data.get('num_questions', 5), 10)  # Max 10 questions
        difficulty = data.get('difficulty', 'Medium')

        quiz_prompt = f"""Generate {num_questions} multiple choice questions on {topic}
        for GATE preparation. Difficulty level: {difficulty}.

        Format each question as:
        Q1: [Question text]
        A) [Option A]
        B) [Option B]
        C) [Option C]
        D) [Option D]
        Correct Answer: [Letter]
        Explanation: [Brief explanation]

        Make sure questions are relevant to GATE syllabus."""

        quiz_content = ai_service.get_educational_response(quiz_prompt)

        return jsonify({
            "topic": topic,
            "difficulty": difficulty,
            "num_questions": num_questions,
            "quiz_content": quiz_content,
            "generated_at": datetime.now().isoformat()
        }), 200

    except Exception as e:
        logger.error(f"Quiz generation error: {str(e)}")
        return jsonify({"error": "Failed to generate quiz"}), 500

@app.route("/api/explain-concept", methods=["POST"])
def explain_concept():
    """Explain a specific concept in detail"""
    try:
        data = request.get_json()
        concept = data.get('concept')
        level = data.get('level', 'intermediate')  # basic, intermediate, advanced

        if not concept:
            return jsonify({"error": "Concept is required"}), 400

        explanation_prompt = f"""Explain the concept of "{concept}" for GATE preparation.
        Level: {level}

        Please provide:
        1. Definition and basic understanding
        2. Key formulas or principles
        3. Important applications
        4. Common mistakes to avoid
        5. Practice problem example
        6. Related concepts to study

        Make it comprehensive but easy to understand."""

        explanation = ai_service.get_educational_response(explanation_prompt)

        return jsonify({
            "concept": concept,
            "level": level,
            "explanation": explanation,
            "generated_at": datetime.now().isoformat()
        }), 200

    except Exception as e:
        logger.error(f"Concept explanation error: {str(e)}")
        return jsonify({"error": "Failed to explain concept"}), 500

@app.route("/api/health", methods=["GET"])
def health_check():
    """Health check endpoint"""
    return jsonify({
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0"
    }), 200

if __name__ == "__main__":
    app.run(debug=True, host='0.0.0.0', port=5000)