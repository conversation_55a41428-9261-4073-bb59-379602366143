from flask import Flask, render_template, request
import openai

app = Flask(__name__)
openai.api_key = "your-openai-api-key"

def get_gpt_answer(prompt):
    response = openai.ChatCompletion.create(
        model="gpt-4",
        messages=[{"role": "user", "content": prompt}]
    )
    return response['choices'][0]['message']['content']

@app.route("/")
def home():
    return render_template("index.html")

@app.route("/ask", methods=["POST"])
def ask():
    question = request.form["question"]
    answer = get_gpt_answer(question)
    return render_template("index.html", question=question, answer=answer)