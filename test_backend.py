#!/usr/bin/env python3
"""
Test script for Learn with Surya AI Tutoring Platform
"""

import requests
import json
import time

BASE_URL = "http://localhost:5000"

def test_health_check():
    """Test the health check endpoint"""
    print("🔍 Testing Health Check...")
    try:
        response = requests.get(f"{BASE_URL}/api/health")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Health Check: {data['status']}")
            return True
        else:
            print(f"❌ Health Check Failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health Check Error: {e}")
        return False

def test_ask_question():
    """Test the ask question endpoint"""
    print("\n🤖 Testing Ask Question...")
    try:
        headers = {"Content-Type": "application/json"}
        data = {"question": "What is Ohm's law?", "subject_branch": "electrical"}
        response = requests.post(f"{BASE_URL}/ask", json=data, headers=headers)

        if response.status_code == 200:
            result = response.json()
            print("✅ Ask Question: Success")
            print(f"   Subject: {result.get('subject_branch', 'N/A')}")
            print(f"   Actor Voice: {result.get('actor_voice', 'N/A')}")
            print(f"   Audio URL: {result.get('audio_url', 'N/A')}")
            print(f"   Answer Preview: {result.get('answer', '')[:100]}...")
            return True
        else:
            print(f"❌ Ask Question Failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Ask Question Error: {e}")
        return False

def test_ask_question_auto_detect():
    """Test the ask question endpoint with auto subject detection"""
    print("\n🤖 Testing Ask Question (Auto-detect Subject)...")
    try:
        headers = {"Content-Type": "application/json"}
        data = {"question": "Explain binary search algorithm"}
        response = requests.post(f"{BASE_URL}/ask", json=data, headers=headers)

        if response.status_code == 200:
            result = response.json()
            print("✅ Ask Question (Auto-detect): Success")
            print(f"   Detected Subject: {result.get('subject_branch', 'N/A')}")
            print(f"   Actor Voice: {result.get('actor_voice', 'N/A')}")
            print(f"   Audio URL: {result.get('audio_url', 'N/A')}")
            return True
        else:
            print(f"❌ Ask Question (Auto-detect) Failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Ask Question (Auto-detect) Error: {e}")
        return False

def test_quote_endpoint():
    """Test the motivational quote endpoint"""
    print("\n💬 Testing Motivational Quote...")
    try:
        response = requests.get(f"{BASE_URL}/quote")

        if response.status_code == 200:
            result = response.json()
            print("✅ Motivational Quote: Success")
            print(f"   Quote: \"{result.get('quote', 'N/A')}\"")
            print(f"   Author: {result.get('author', 'N/A')}")
            print(f"   Category: {result.get('category', 'N/A')}")
            return True
        else:
            print(f"❌ Motivational Quote Failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Motivational Quote Error: {e}")
        return False

def test_user_registration():
    """Test user registration"""
    print("\n👤 Testing User Registration...")
    try:
        headers = {"Content-Type": "application/json"}
        data = {
            "username": f"testuser_{int(time.time())}",
            "email": f"test_{int(time.time())}@example.com",
            "password": "testpassword123"
        }
        response = requests.post(f"{BASE_URL}/api/register", json=data, headers=headers)
        
        if response.status_code == 201:
            result = response.json()
            print(f"✅ Registration: Success for user {result.get('username')}")
            return True, data
        else:
            print(f"❌ Registration Failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False, None
    except Exception as e:
        print(f"❌ Registration Error: {e}")
        return False, None

def test_user_login(user_data):
    """Test user login"""
    print("\n🔐 Testing User Login...")
    try:
        headers = {"Content-Type": "application/json"}
        data = {
            "username": user_data["username"],
            "password": user_data["password"]
        }
        response = requests.post(f"{BASE_URL}/api/login", json=data, headers=headers)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Login: Success for user {result.get('username')}")
            return True, response.cookies
        else:
            print(f"❌ Login Failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False, None
    except Exception as e:
        print(f"❌ Login Error: {e}")
        return False, None

def test_generate_quiz():
    """Test quiz generation"""
    print("\n📝 Testing Quiz Generation...")
    try:
        headers = {"Content-Type": "application/json"}
        data = {
            "topic": "Digital Electronics",
            "num_questions": 3,
            "difficulty": "Medium"
        }
        response = requests.post(f"{BASE_URL}/api/generate-quiz", json=data, headers=headers)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Quiz Generation: Success")
            print(f"   Topic: {result.get('topic')}")
            print(f"   Questions: {result.get('num_questions')}")
            return True
        else:
            print(f"❌ Quiz Generation Failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Quiz Generation Error: {e}")
        return False

def test_explain_concept():
    """Test concept explanation"""
    print("\n📚 Testing Concept Explanation...")
    try:
        headers = {"Content-Type": "application/json"}
        data = {
            "concept": "Fourier Transform",
            "level": "intermediate"
        }
        response = requests.post(f"{BASE_URL}/api/explain-concept", json=data, headers=headers)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Concept Explanation: Success")
            print(f"   Concept: {result.get('concept')}")
            print(f"   Level: {result.get('level')}")
            return True
        else:
            print(f"❌ Concept Explanation Failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Concept Explanation Error: {e}")
        return False

def main():
    """Run all tests"""
    print("🎓 Learn with Surya AI Tutoring Platform Tests")
    print("=" * 50)

    tests_passed = 0
    total_tests = 0

    # Test 1: Health Check
    total_tests += 1
    if test_health_check():
        tests_passed += 1

    # Test 2: Ask Question with Subject
    total_tests += 1
    if test_ask_question():
        tests_passed += 1

    # Test 3: Ask Question with Auto-detect
    total_tests += 1
    if test_ask_question_auto_detect():
        tests_passed += 1

    # Test 4: Motivational Quote
    total_tests += 1
    if test_quote_endpoint():
        tests_passed += 1

    # Summary
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {tests_passed}/{total_tests} tests passed")

    if tests_passed == total_tests:
        print("🎉 All tests passed! AI Tutoring Platform is working correctly.")
        print("\n🎯 Features Verified:")
        print("   ✅ AI-powered question answering")
        print("   ✅ Subject branch detection")
        print("   ✅ Actor voice mapping")
        print("   ✅ TTS audio URL generation")
        print("   ✅ Motivational quotes")
    else:
        print(f"⚠️  {total_tests - tests_passed} test(s) failed. Check the output above.")

    print("\n💡 Tips:")
    print("   - Make sure the backend server is running on http://localhost:5000")
    print("   - Check that your OpenAI API key is properly configured")
    print("   - The TTS feature is currently simulated (returns mock URLs)")
    print("   - To integrate real TTS, replace the simulate_tts_generation function")

if __name__ == "__main__":
    main()
