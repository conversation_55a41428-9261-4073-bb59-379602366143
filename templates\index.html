<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Learn with <PERSON><PERSON></title>
    <link rel="stylesheet" href="/static/style.css">
</head>
<body>
    <h1>🎓 Learn with <PERSON><PERSON> – Learn to Earn</h1>
    <form method="POST" action="/ask">
        <input type="text" name="question" id="question" placeholder="Ask a GATE topic..." required>
        <button type="submit">Ask</button>
        <button type="button" onclick="startVoice()">🎤 Speak</button>
    </form>
    {% if answer %}
        <div id="response">{{ answer }}</div>
        <button onclick="speakAnswer('{{ answer }}')">🔊 Hear Answer</button>
    {% endif %}
    <script>
        function startVoice() {
            const recognition = new webkitSpeechRecognition();
            recognition.lang = 'en-IN';
            recognition.onresult = (event) => {
                document.getElementById('question').value = event.results[0][0].transcript;
            };
            recognition.start();
        }

        function speakAnswer(text) {
            const msg = new SpeechSynthesisUtterance(text);
            msg.voice = speechSynthesis.getVoices()[0];
            speechSynthesis.speak(msg);
        }
    </script>
</body>
</html>